#!/usr/bin/env python3
"""
Test script to verify the license system functionality
"""

import tkinter as tk
from tkinter import messagebox
import json
import os
from datetime import datetime, timedelta
from pathlib import Path
import base64

# Test the license system
def test_license_system():
    print("Testing AI PC Shortcuts Manager Pro License System...")
    
    # Test paths
    app_dir = Path.home() / ".amssoftx" / "ai_shortcuts"
    license_file = app_dir / "license_info.json"
    install_date_file = app_dir / "install_date.txt"
    
    print(f"App directory: {app_dir}")
    print(f"License file: {license_file}")
    print(f"Install date file: {install_date_file}")
    
    # Check if files exist
    print(f"\nFile status:")
    print(f"App directory exists: {app_dir.exists()}")
    print(f"License file exists: {license_file.exists()}")
    print(f"Install date file exists: {install_date_file.exists()}")
    
    # Check license status
    if license_file.exists():
        try:
            with open(license_file, 'r') as f:
                license_data = json.load(f)
                print(f"\nLicense data found:")
                print(f"License key: {license_data.get('license_key', 'Not found')}")
                print(f"Activated date: {license_data.get('activated_date', 'Not found')}")
                
                if license_data.get('license_key') == "AMSSoftx-AI-2319-2025":
                    print("✅ Valid license key found!")
                else:
                    print("❌ Invalid license key")
        except Exception as e:
            print(f"Error reading license file: {e}")
    else:
        print("\n❌ No license file found")
    
    # Check install date
    if install_date_file.exists():
        try:
            with open(install_date_file, 'r') as f:
                date_str = f.read().strip()
                try:
                    # Try to decode if base64
                    decoded = base64.b64decode(date_str).decode('utf-8')
                    install_date = datetime.strptime(decoded, '%Y-%m-%d %H:%M:%S')
                except:
                    install_date = datetime.strptime(date_str, '%Y-%m-%d %H:%M:%S')
                
                days_since_install = (datetime.now() - install_date).days
                days_remaining = max(0, 30 - days_since_install)
                
                print(f"\nTrial information:")
                print(f"Install date: {install_date}")
                print(f"Days since install: {days_since_install}")
                print(f"Trial days remaining: {days_remaining}")
                
                if days_remaining > 0:
                    print("✅ Trial period active")
                else:
                    print("❌ Trial period expired")
                    
        except Exception as e:
            print(f"Error reading install date: {e}")
    else:
        print("\n❌ No install date file found")

def simulate_license_activation():
    """Simulate license activation"""
    print("\n" + "="*50)
    print("SIMULATING LICENSE ACTIVATION")
    print("="*50)
    
    app_dir = Path.home() / ".amssoftx" / "ai_shortcuts"
    license_file = app_dir / "license_info.json"
    
    # Create license data
    license_data = {
        'license_key': 'AMSSoftx-AI-2319-2025',
        'activated_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'user_id': 'test-user-12345'
    }
    
    try:
        app_dir.mkdir(parents=True, exist_ok=True)
        with open(license_file, 'w') as f:
            json.dump(license_data, f, indent=2)
        print("✅ License activated successfully!")
        print(f"License file created at: {license_file}")
    except Exception as e:
        print(f"❌ Error activating license: {e}")

def reset_trial():
    """Reset trial period (for testing)"""
    print("\n" + "="*50)
    print("RESETTING TRIAL PERIOD")
    print("="*50)
    
    app_dir = Path.home() / ".amssoftx" / "ai_shortcuts"
    
    # Remove all tracking files
    files_to_remove = [
        app_dir / "license_info.json",
        app_dir / "install_date.txt",
        app_dir / ".install_track",
        app_dir / ".app_signature",
        Path.home() / ".amssoftx_track"
    ]
    
    for file_path in files_to_remove:
        if file_path.exists():
            try:
                file_path.unlink()
                print(f"✅ Removed: {file_path}")
            except Exception as e:
                print(f"❌ Error removing {file_path}: {e}")
        else:
            print(f"ℹ️  Not found: {file_path}")

if __name__ == "__main__":
    print("AI PC Shortcuts Manager Pro - License System Test")
    print("="*60)
    
    while True:
        print("\nOptions:")
        print("1. Check current license status")
        print("2. Simulate license activation")
        print("3. Reset trial period")
        print("4. Exit")
        
        choice = input("\nEnter your choice (1-4): ").strip()
        
        if choice == "1":
            test_license_system()
        elif choice == "2":
            simulate_license_activation()
        elif choice == "3":
            reset_trial()
        elif choice == "4":
            print("Goodbye!")
            break
        else:
            print("Invalid choice. Please try again.")
